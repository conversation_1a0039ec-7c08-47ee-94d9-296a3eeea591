"use client";
import { useState, useRef, useEffect } from "react";
import { motion, useMotionValue, useSpring, useTransform } from "framer-motion";
import Link from "next/link";
import works from "@/data/artistic-works.json";

interface Position {
  x: number;
  y: number;
}

export default function SpatialCanvas() {
  const [activeElement, setActiveElement] = useState<string | null>(null);
  const [cursorPosition, setCursorPosition] = useState<Position>({ x: 0, y: 0 });
  const [showNavigation, setShowNavigation] = useState(false);
  const [mounted, setMounted] = useState(false);
  const canvasRef = useRef<HTMLDivElement>(null);
  
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  
  const springConfig = { damping: 25, stiffness: 700 };
  const x = useSpring(mouseX, springConfig);
  const y = useSpring(mouseY, springConfig);

  // Create transforms for cursor - must be called before any conditional returns
  const cursorX = useTransform(x, (value) => value - 8);
  const cursorY = useTransform(y, (value) => value - 8);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const handleMouseMove = (e: MouseEvent) => {
      setCursorPosition({ x: e.clientX, y: e.clientY });
      mouseX.set(e.clientX);
      mouseY.set(e.clientY);
    };

    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, [mouseX, mouseY, mounted]);

  // Spatial positions for elements (inspired by Cargo sites)
  const spatialElements = [
    {
      id: "name",
      component: (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, delay: 0.2 }}
          className="select-none"
        >
          <h1 className="artistic-heading text-2xl md:text-3xl font-medium tracking-tight">
            Mehdi Asadi
          </h1>
        </motion.div>
      ),
      position: { top: "10%", left: "6%" },
      size: "large"
    },
    {
      id: "subtitle",
      component: (
        <motion.div
          initial={{ opacity: 0, x: -30 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="select-none"
        >
          <p className="artistic-body text-lg text-muted max-w-xs leading-relaxed">
            Designer & Performance Artist
          </p>
        </motion.div>
      ),
      position: { top: "18%", left: "6%" },
      size: "medium"
    },
    {
      id: "location",
      component: (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.2 }}
          className="select-none"
        >
          <p className="text-sm text-muted uppercase tracking-wider">
            Tehran, Iran
          </p>
        </motion.div>
      ),
      position: { top: "85%", left: "10%" },
      size: "small"
    },
    {
      id: "year",
      component: (
        <motion.div
          initial={{ opacity: 0, rotate: -5 }}
          animate={{ opacity: 1, rotate: 0 }}
          transition={{ duration: 0.8, delay: 1.4 }}
          className="select-none"
        >
          <p className="text-sm text-muted">
            2024
          </p>
        </motion.div>
      ),
      position: { top: "20%", right: "15%" },
      size: "small"
    },
    {
      id: "work-1",
      component: (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 1.6 }}
          whileHover={{ scale: 1.05, y: -5 }}
          className="cursor-pointer select-none group"
          onClick={() => setActiveElement(activeElement === "work-1" ? null : "work-1")}
        >
          <div className="space-y-2">
            <div className="w-48 h-32 bg-artistic-red/10 artistic-border group-hover:bg-artistic-red/20 transition-colors" />
            <div className="space-y-1">
              <h3 className="text-sm font-medium group-hover:text-artistic-red transition-colors">
                {works[0]?.title.split(" — ")[0]}
              </h3>
              <p className="text-xs text-muted">
                {works[0]?.year}
              </p>
            </div>
          </div>
        </motion.div>
      ),
      position: { top: "45%", right: "25%" },
      size: "medium"
    },
    {
      id: "work-2",
      component: (
        <motion.div
          initial={{ opacity: 0, x: 30 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 2 }}
          whileHover={{ scale: 1.05, y: -5 }}
          className="cursor-pointer select-none group"
          onClick={() => setActiveElement(activeElement === "work-2" ? null : "work-2")}
        >
          <div className="space-y-2">
            <div className="w-40 h-28 bg-artistic-blue/10 artistic-border group-hover:bg-artistic-blue/20 transition-colors" />
            <div className="space-y-1">
              <h3 className="text-sm font-medium group-hover:text-artistic-blue transition-colors">
                {works[1]?.title.split(" — ")[0]}
              </h3>
              <p className="text-xs text-muted">
                {works[1]?.year}
              </p>
            </div>
          </div>
        </motion.div>
      ),
      position: { bottom: "25%", right: "8%" },
      size: "medium"
    },
    {
      id: "contact",
      component: (
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 2.4 }}
          className="select-none"
        >
          <div className="space-y-2">
            <p className="text-xs text-muted uppercase tracking-wider">
              Contact
            </p>
            <div className="space-y-1 text-sm">
              <a
                href="mailto:<EMAIL>"
                className="hover:text-artistic-red transition-colors cursor-pointer block"
              >
                <EMAIL>
              </a>
              <a
                href="https://www.instagram.com/mehdi_assadii/"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-artistic-red transition-colors cursor-pointer block"
              >
                @mehdi_assadii
              </a>
            </div>
          </div>
        </motion.div>
      ),
      position: { bottom: "15%", left: "60%" },
      size: "small"
    },
    {
      id: "nav-hint",
      component: (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 3 }}
          className="select-none"
        >
          <p className="text-xs text-muted/30 uppercase tracking-wider">
            Space bar to navigate
          </p>
        </motion.div>
      ),
      position: { bottom: "8%", left: "50%", transform: "translateX(-50%)" },
      size: "small"
    },
    {
      id: "work-3",
      component: (
        <motion.div
          initial={{ opacity: 0, rotate: 5 }}
          animate={{ opacity: 1, rotate: 0 }}
          transition={{ duration: 0.8, delay: 2.2 }}
          whileHover={{ scale: 1.05, rotate: -2 }}
          className="cursor-pointer select-none group"
          onClick={() => setActiveElement(activeElement === "work-3" ? null : "work-3")}
        >
          <div className="space-y-2">
            <div className="w-32 h-20 bg-artistic-yellow/10 artistic-border group-hover:bg-artistic-yellow/20 transition-colors" />
            <div className="space-y-1">
              <h3 className="text-xs font-medium group-hover:text-artistic-yellow transition-colors">
                {works[2]?.title.split(" — ")[0]}
              </h3>
              <p className="text-xs text-muted/60">
                {works[2]?.year}
              </p>
            </div>
          </div>
        </motion.div>
      ),
      position: { top: "65%", left: "25%" },
      size: "small"
    },
    {
      id: "work-4",
      component: (
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 2.6 }}
          whileHover={{ scale: 1.05, x: 5 }}
          className="cursor-pointer select-none group"
          onClick={() => setActiveElement(activeElement === "work-4" ? null : "work-4")}
        >
          <div className="space-y-2">
            <div className="w-36 h-24 bg-artistic-red/15 artistic-border group-hover:bg-artistic-red/25 transition-colors" />
            <div className="space-y-1">
              <h3 className="text-xs font-medium group-hover:text-artistic-red transition-colors">
                {works[5]?.title.split(" — ")[0]}
              </h3>
              <p className="text-xs text-muted/60">
                {works[5]?.year}
              </p>
            </div>
          </div>
        </motion.div>
      ),
      position: { top: "40%", left: "70%" },
      size: "small"
    },
    {
      id: "work-5",
      component: (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 3 }}
          whileHover={{ scale: 1.05, y: -10 }}
          className="cursor-pointer select-none group"
          onClick={() => setActiveElement(activeElement === "work-5" ? null : "work-5")}
        >
          <div className="space-y-2">
            <div className="w-28 h-18 bg-artistic-blue/15 artistic-border group-hover:bg-artistic-blue/25 transition-colors" />
            <div className="space-y-1">
              <h3 className="text-xs font-medium group-hover:text-artistic-blue transition-colors">
                {works[6]?.title.split(" — ")[0]}
              </h3>
              <p className="text-xs text-muted/60">
                {works[6]?.year}
              </p>
            </div>
          </div>
        </motion.div>
      ),
      position: { bottom: "35%", left: "15%" },
      size: "small"
    }
  ];

  // Don't render until mounted to avoid hydration mismatch
  if (!mounted) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-muted animate-pulse">
          Loading...
        </div>
      </div>
    );
  }

  return (
    <div
      ref={canvasRef}
      className="relative w-full h-screen overflow-hidden bg-background spatial-canvas"
      onKeyDown={(e) => {
        if (e.key === ' ') {
          e.preventDefault();
          setShowNavigation(!showNavigation);
        }
      }}
      tabIndex={0}
    >
      {/* Experimental Navigation Overlay */}
      {showNavigation && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-background/95 backdrop-blur-sm z-40 flex items-center justify-center"
          onClick={() => setShowNavigation(false)}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 max-w-6xl mx-auto px-8">
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.1 }}
              className="space-y-4 cursor-pointer group"
              onClick={(e) => {
                e.stopPropagation();
                setActiveElement("works");
                setShowNavigation(false);
              }}
            >
              <h3 className="artistic-heading text-2xl font-medium group-hover:text-artistic-red transition-colors">
                Works
              </h3>
              <p className="text-sm text-muted">
                Visual investigations & cultural research
              </p>
            </motion.div>

            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.2 }}
              className="space-y-4 cursor-pointer group"
              onClick={(e) => {
                e.stopPropagation();
                setActiveElement("about");
                setShowNavigation(false);
              }}
            >
              <h3 className="artistic-heading text-2xl font-medium group-hover:text-artistic-blue transition-colors">
                Practice
              </h3>
              <p className="text-sm text-muted">
                Approach & methodology
              </p>
            </motion.div>

            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="space-y-4 cursor-pointer group"
              onClick={(e) => {
                e.stopPropagation();
                setActiveElement("contact");
                setShowNavigation(false);
              }}
            >
              <h3 className="artistic-heading text-2xl font-medium group-hover:text-artistic-yellow transition-colors">
                Connect
              </h3>
              <p className="text-sm text-muted">
                Collaboration & dialogue
              </p>
            </motion.div>

            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="space-y-4 cursor-pointer group"
            >
              <Link
                href="/cv"
                onClick={() => setShowNavigation(false)}
                className="block"
              >
                <h3 className="artistic-heading text-2xl font-medium group-hover:text-artistic-blue transition-colors">
                  CV
                </h3>
                <p className="text-sm text-muted">
                  Experience & education
                </p>
              </Link>
            </motion.div>
          </div>

          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
            <p className="text-xs text-muted/60 uppercase tracking-wider">
              Press space or click to close
            </p>
          </div>
        </motion.div>
      )}

      {/* Navigation trigger hint */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 4 }}
        className="fixed top-8 right-8 z-30"
      >
        <button
          onClick={() => setShowNavigation(true)}
          className="text-xs text-muted/60 uppercase tracking-wider hover:text-foreground transition-colors"
        >
          Menu
        </button>
      </motion.div>

      {/* Custom cursor */}
      <motion.div
        className="fixed top-0 left-0 w-3 h-3 bg-foreground rounded-full pointer-events-none z-50 opacity-80"
        style={{
          x: cursorX,
          y: cursorY,
        }}
      />

      {/* Detail overlays */}
      {activeElement && activeElement.startsWith("work-") && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          className="fixed inset-0 bg-background/90 backdrop-blur-sm z-30 flex items-center justify-center p-8"
          onClick={() => setActiveElement(null)}
        >
          <motion.div
            initial={{ y: 30 }}
            animate={{ y: 0 }}
            className="max-w-2xl mx-auto space-y-6"
            onClick={(e) => e.stopPropagation()}
          >
            {(() => {
              const workIndex = parseInt(activeElement.split("-")[1]) - 1;
              const work = works[workIndex];
              if (!work) return null;

              return (
                <>
                  <div className="space-y-4">
                    <h2 className="artistic-heading text-3xl font-medium">
                      {work.title}
                    </h2>
                    <div className="flex items-center space-x-4 text-sm text-muted">
                      <span>{work.role}</span>
                      <span>•</span>
                      <span>{work.year}</span>
                    </div>
                  </div>

                  <div className="w-full h-64 bg-muted/10 artistic-border" />

                  <p className="artistic-body text-lg leading-relaxed">
                    {work.description}
                  </p>

                  <button
                    onClick={() => setActiveElement(null)}
                    className="text-sm text-muted hover:text-foreground transition-colors"
                  >
                    Close
                  </button>
                </>
              );
            })()}
          </motion.div>
        </motion.div>
      )}

      {/* Spatial elements */}
      {spatialElements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute"
          style={element.position}
          whileHover={{ scale: 1.02 }}
        >
          {element.component}
        </motion.div>
      ))}

      {/* Background artistic elements */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 0.02, scale: 1 }}
        transition={{ duration: 4, delay: 1 }}
        className="absolute top-1/3 left-1/3 w-96 h-96 bg-artistic-yellow rounded-full -z-10"
      />
      
      <motion.div
        initial={{ opacity: 0, rotate: 0 }}
        animate={{ opacity: 0.03, rotate: 45 }}
        transition={{ duration: 6, delay: 2 }}
        className="absolute bottom-1/4 left-1/4 w-64 h-64 bg-artistic-blue -z-10"
      />
    </div>
  );
}
